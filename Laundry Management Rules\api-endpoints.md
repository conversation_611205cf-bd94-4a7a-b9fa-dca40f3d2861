# API Endpoints Reference - Laundry Management System

## Base URLs
- **Primary:** `/api/v1/`
- **Legacy:** `/api/` (for compatibility)
- **Direct:** `/` (for frontend compatibility)

## Authentication Routes (`/auth`)

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| POST | `/auth/login` | Đăng nhập | Public |
| POST | `/auth/logout` | Đăng xuất | Private |
| POST | `/auth/refresh` | Refresh access token | Public |
| GET | `/auth/me` | Lấy thông tin user hiện tại | Private |
| POST | `/auth/register` | Đăng ký user mới | Admin only |

## Customer Routes (`/customers`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/customers` | L<PERSON>y danh sách khách hàng | `page`, `limit`, `search`, `sort`, `order` |
| GET | `/customers/:id` | Lấy thông tin khách hàng theo ID | - |
| POST | `/customers` | Tạo khách hàng mới | - |
| PUT | `/customers/:id` | Cập nhật thông tin khách hàng | - |
| DELETE | `/customers/:id` | Xóa khách hàng (soft delete) | - |

## Product Routes (`/products`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/products/unit-types` | Lấy danh sách unit types | - |
| GET | `/products` | Lấy danh sách sản phẩm | `page`, `limit`, `search`, `unit_type`, `is_active`, `sort`, `order` |
| GET | `/products/:id` | Lấy thông tin sản phẩm theo ID | - |
| POST | `/products` | Tạo sản phẩm mới (auto-generate code) | - |
| PUT | `/products/:id` | Cập nhật thông tin sản phẩm | - |
| DELETE | `/products/:id` | Xóa sản phẩm (soft delete) | - |

## Contract Routes (`/contracts`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/contracts` | Lấy tất cả hợp đồng | `page`, `limit`, `customer_id`, `status` |
| GET | `/contracts/:id` | Lấy thông tin hợp đồng theo ID | - |
| POST | `/contracts` | Tạo hợp đồng mới | - |
| PUT | `/contracts/:id` | Cập nhật thông tin hợp đồng | - |
| DELETE | `/contracts/:id` | Xóa hợp đồng | - |
| GET | `/contracts/statuses` | Lấy danh sách trạng thái hợp đồng | - |
| GET | `/contracts/customer/:customerId` | Lấy hợp đồng theo khách hàng | - |

## Contract Price Routes (`/contract-prices`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/contract-prices` | Lấy tất cả đơn giá | `page`, `limit`, `contract_id`, `product_id`, `is_active` |
| GET | `/contract-prices/:id` | Lấy đơn giá theo ID | - |
| POST | `/contract-prices` | Tạo đơn giá mới | - |
| PUT | `/contract-prices/:id` | Cập nhật đơn giá | - |
| DELETE | `/contract-prices/:id` | Xóa đơn giá | - |
| GET | `/contract-prices/contract/:contractId` | Lấy đơn giá theo hợp đồng | - |
| POST | `/contract-prices/bulk-update` | Cập nhật đơn giá hàng loạt | - |

## Daily Production Routes (`/daily-production`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/daily-production` | Lấy danh sách sản lượng hàng ngày | `page`, `limit`, `contract_id`, `product_id`, `production_date`, `status` |
| GET | `/daily-production/:id` | Lấy sản lượng theo ID | - |
| POST | `/daily-production` | Tạo sản lượng mới | - |
| PUT | `/daily-production/:id` | Cập nhật sản lượng | - |
| DELETE | `/daily-production/:id` | Xóa sản lượng | - |
| POST | `/daily-production/bulk` | Tạo sản lượng hàng loạt | - |
| GET | `/daily-production/summary` | Lấy tổng kết sản lượng | `start_date`, `end_date`, `contract_id` |
| GET | `/daily-production/report` | Lấy báo cáo sản lượng | `start_date`, `end_date`, `contract_id`, `status` |

## Receivables Routes (`/receivables`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/receivables` | Lấy danh sách công nợ phải thu | `page`, `limit`, `customer_id`, `contract_id`, `status` |
| GET | `/receivables/:id` | Lấy công nợ theo ID | - |
| POST | `/receivables` | Tạo công nợ mới | - |
| PUT | `/receivables/:id` | Cập nhật công nợ | - |
| DELETE | `/receivables/:id` | Xóa công nợ | - |

## Payment Routes (`/payments`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/payments` | Lấy danh sách thanh toán | `page`, `limit`, `customer_id`, `receivable_id` |
| GET | `/payments/:id` | Lấy thanh toán theo ID | - |
| POST | `/payments` | Tạo thanh toán mới | - |
| PUT | `/payments/:id` | Cập nhật thanh toán | - |
| DELETE | `/payments/:id` | Xóa thanh toán | - |

## Dashboard Routes (`/dashboard`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/dashboard/stats` | Lấy thống kê tổng quan hệ thống | - |
| GET | `/dashboard/production-chart` | Lấy dữ liệu biểu đồ sản lượng | `year`, `month` |
| GET | `/dashboard/top-products` | Lấy top sản phẩm theo sản lượng | `year`, `month`, `limit` |
| GET | `/dashboard/debt-summary` | Lấy tổng kết công nợ | - |
| GET | `/dashboard/monthly-summary` | Lấy tổng kết theo tháng | `year`, `month` |
| GET | `/dashboard/all` | Lấy tất cả dữ liệu dashboard | - |

## Report Routes (`/reports`)

| Method | Endpoint | Description | Query Params |
|--------|----------|-------------|--------------|
| GET | `/reports/aging-analysis` | Lấy báo cáo aging analysis | `customerId`, `asOfDate`, `includeZeroBalance` |
| GET | `/reports/debt-summary` | Lấy tổng kết công nợ | `customerId`, `contractId`, `status` |
| GET | `/reports/production-summary` | Lấy tổng kết sản lượng | `start_date`, `end_date`, `contract_id` |

## Common Query Parameters

### Pagination
- `page`: Số trang (default: 1)
- `limit`: Số lượng items per page (default: 10, max: 1000)

### Sorting
- `sort`: Cột để sắp xếp (varies by endpoint)
- `order`: Thứ tự sắp xếp (`ASC`/`DESC`, default: `DESC`)

### Filtering
- `search`: Từ khóa tìm kiếm
- `is_active`: Filter theo trạng thái (`true`/`false`)
- `status`: Filter theo trạng thái (varies by entity)

### Date Filtering
- `start_date`: Ngày bắt đầu (format: YYYY-MM-DD)
- `end_date`: Ngày kết thúc (format: YYYY-MM-DD)
- `production_date`: Ngày sản xuất (format: YYYY-MM-DD)

## Response Format

### Success Response
```json
{
  "success": true,
  "data": {...},
  "message": "Success message in Vietnamese",
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error message in Vietnamese",
    "details": ["Detailed error information"],
    "statusCode": 400
  }
}
```

## Authentication
- Most endpoints require JWT authentication (currently disabled for development)
- Use `Authorization: Bearer <token>` header
- Admin-only endpoints require admin role

## Notes
- All endpoints support multiple URL formats for compatibility
- Vietnamese language is used for all user-facing messages
- Date format: dd/mm/yyyy for input, YYYY-MM-DD for API
- Currency format: Vietnamese formatting (1.000.000 VND)
