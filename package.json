{"name": "laundry-management-app", "version": "1.0.0", "description": "Laundry Management System with Contract, Product, and Debt Management", "main": "server/src/server.js", "scripts": {"start": "cd server && npm start", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "dev": "concurrently --kill-others-on-fail --prefix \"[{name}]\" --names \"SERVER,CLIENT\" --prefix-colors \"blue,green\" \"npm run server\" \"npm run client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "dev:silent": "concurrently --kill-others-on-fail \"npm run server\" \"npm run client\"", "test": "echo \"Error: no test specified\" && exit 1", "build": "cd client && npm install && npm run build && cd ../server && npm install", "build:client": "cd client && npm run build", "install-deps": "cd server && npm install && cd ../client && npm install", "clean": "rm -rf node_modules server/node_modules client/node_modules", "health": "curl http://localhost:8500/health", "heroku-postbuild": "cd server && npm install && cd ../client && npm install && npm run build"}, "keywords": ["customer", "management", "react", "nodejs", "postgresql"], "author": "IVC", "license": "ISC", "dependencies": {"concurrently": "^8.2.2"}, "devDependencies": {}, "engines": {"node": ">=18.x"}}